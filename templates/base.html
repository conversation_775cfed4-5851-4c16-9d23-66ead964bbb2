<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Explore the music and videos of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a talented artist."
    />
    <meta
      name="keywords"
      content="iamshiuba, music, videos, musician, entertainment, artist, singles, music videos"
    />
    <meta name="theme-color" content="#ff0000" />
    <title>IamSHIUBA</title>

    <link
      rel="icon"
      type="image/svg+xml"
      href="{{ url_for('static', filename='img/is_web.svg') }}"
      sizes="any"
    />
    <link
      rel="apple-touch-icon"
      href="{{ url_for('static', filename='img/icons/icon-192x192.png') }}"
    />
    <link
      rel="manifest"
      href="{{ url_for('static', filename='manifest.json') }}"
    />

    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com" />
    <link rel="dns-prefetch" href="https://cdn.jsdelivr.net" />
    <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
    <link rel="dns-prefetch" href="https://fonts.gstatic.com" />

    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css"
      integrity="sha512-Kc323vGBEqzTmouAECnVceyQqyqdsSiqLQISBL29aUW4U/M7pSPA/gEUZQqv1cwx4OnYxTxve5UMg5GT6L4JJg=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/gh/lipis/flag-icons@7.2.3/css/flag-icons.min.css"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='dist/output.css') }}"
    />

    <meta property="og:title" content="IamSHIUBA" />
    <meta
      property="og:description"
      content="Explore the music and videos of IamSHIUBA, a talented artist."
    />
    <meta
      property="og:image"
      content="{{ url_for('static', filename='img/iamshiuba_web.svg', _external=True) }}"
    />
    <meta property="og:url" content="{{ request.url }}" />
    <meta name="twitter:card" content="summary_large_image" />
  </head>

  <body>
    {% include 'partials/navbar.html' %}

    <main>{% block content %}{% endblock %}</main>
    
    {% include 'partials/footer.html' %} {% include 'partials/m-navbar.html' %}

    <script src="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.js"></script>
    <script src="{{ url_for('static', filename='js/utils/CurrentPageLink.js') }}"></script>
    <script src="{{ url_for('static', filename='js/utils/ThemeSelector.js') }}"></script>
    <script src="{{ url_for('static', filename='js/utils/CounterAnimation.js') }}"></script>
    <script src="{{ url_for('static', filename='js/utils/ImageOptimizer.js') }}"></script>
    <script src="{{ url_for('static', filename='js/utils/Translations.js') }}"></script>
    <script src="{{ url_for('static', filename='js/register-sw.js') }}"></script>
  </body>
</html>
