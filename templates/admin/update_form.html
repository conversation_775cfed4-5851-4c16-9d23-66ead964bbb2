{% extends "base.html" %} {% block content %}
<div
  class="max-w-2xl mx-auto mt-10 p-6 bg-[var(--background-secondary)] rounded-lg shadow-md"
>
  <h1
    class="text-3xl font-bold mb-6 bg-gradient-to-r from-red-500 from-10% to-red-700 to-60% text-transparent bg-clip-text"
  >
    {% if update %}
    <span data-translate="admin_edit_update"></span>
    {% else %}
    <span data-translate="admin_add_update"></span>
    {% endif %}
  </h1>

  <form method="POST">
    <div class="mb-4">
      <label
        for="title"
        data-translate="admin_title_field"
      ></label>
      <input
        type="text"
        id="title"
        name="title"
        class="bg-[var(--background-primary)] border border-[var(--border-color)] text-[var(--text-primary)] text-sm rounded-lg focus:ring-red-500 focus:border-red-500 block w-full p-2.5"
        value="{{ update['title'] if update else '' }}"
        required
      />
    </div>

    <div class="mb-4">
      <label
        for="version"
        data-translate="admin_version_field"
      ></label>
      <input
        type="text"
        id="version"
        name="version"
        class="bg-[var(--background-primary)] border border-[var(--border-color)] text-[var(--text-primary)] text-sm rounded-lg focus:ring-red-500 focus:border-red-500 block w-full p-2.5"
        value="{{ update['version'] if update else '' }}"
      />
    </div>

    <div class="mb-4">
      <label
        for="content"
        data-translate="admin_content_field"
      ></label>
      <textarea id="content" name="content" class="bg-[var(--background-primary)] border border-[var(--border-color)] text-[var(--text-primary)] text-sm rounded-lg focus:ring-red-500 focus:border-red-500 block w-full p-2.5" rows="10" required>
{{ update['content'] if update else '' }}</textarea
      >
    </div>

    <div class="mb-4">
      <label
        for="preview_link"
        data-translate="admin_preview_link_field"
      ></label>
      <input
        type="url"
        id="preview_link"
        name="preview_link"
        class="bg-[var(--background-primary)] border border-[var(--border-color)] text-[var(--text-primary)] text-sm rounded-lg focus:ring-red-500 focus:border-red-500 block w-full p-2.5"
        value="{{ update['preview_link'] if update and update['preview_link'] else '' }}"
      />
    </div>

    <div class="flex space-x-4">
      <button type="submit" class="hBtn" data-translate="admin_save"></button>
      <a
        href="{{ url_for('admin_dashboard') }}"
        class="px-5 py-2.5 bg-gray-500 text-white rounded text-center text-lg transition-all duration-500 ease-in-out hover:bg-gray-600"
        data-translate="admin_cancel"
      ></a>
    </div>
  </form>
</div>
{% endblock %}
