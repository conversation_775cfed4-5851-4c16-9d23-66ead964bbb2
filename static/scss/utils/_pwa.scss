.update-notification {
  @apply fixed bottom-0 left-0 right-0 bg-red-600 text-white shadow-lg z-50 transform translate-y-full transition-transform duration-300 ease-in-out;

  &.show {
    @apply transform-none;
  }

  .update-content {
    @apply container mx-auto p-4 flex flex-col md:flex-row items-center justify-between;
    max-width: 768px;
  }

  .update-icon {
    @apply text-2xl mr-4 hidden md:block;

    i {
      @apply animate-[spin-slow];
    }
  }

  .update-text {
    @apply flex-grow;

    p {
      @apply font-bold text-lg mb-0;
    }

    small {
      @apply text-white/80;
    }
  }

  .update-actions {
    @apply flex mt-3 md:mt-0;
  }

  .update-btn {
    @apply px-4 py-2 rounded text-sm font-medium transition-colors duration-200;

    &.primary {
      @apply bg-white text-red-600 hover:bg-gray-100 ml-2;
    }

    &.secondary {
      @apply bg-transparent border border-white text-white hover:bg-white/10;
    }
  }

  @media (max-width: 640px) {
    .update-text {
      @apply text-center mb-2;
    }

    .update-actions {
      @apply justify-center;
    }
  }
}

.install-button {
  @apply fixed bottom-20 right-5 bg-red-600 text-white px-4 py-2 rounded-full shadow-lg z-40 flex items-center;

  i {
    @apply mr-2;
  }

  &:hover {
    @apply bg-red-700;
  }
}